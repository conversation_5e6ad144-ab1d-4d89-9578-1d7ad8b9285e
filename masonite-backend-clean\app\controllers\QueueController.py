"""Queue Controller for managing background jobs and queue operations"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from masonite.authentication import Auth
from app.services.QueueService import QueueService
from datetime import datetime


class Queue<PERSON><PERSON>roller(Controller):
    """Handle queue operations and background job management"""

    def __init__(self):
        """Initialize queue service"""
        self.queue_service = QueueService()

    def get_queue_stats(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/queue/stats
        Get queue statistics (admin only)
        """
        try:
            # Get current user (admin check would go here)
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get queue statistics
            stats = self.queue_service.get_queue_stats()

            return response.json({
                'queue_stats': stats,
                'timestamp': str(datetime.now())
            }, 200)

        except Exception as e:
            print(f"❌ Queue Stats Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get queue statistics'
                }
            }, 500)

    def queue_test_email(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/queue/test-email
        Queue a test email (for testing purposes)
        """
        # Validate request
        errors = validate.validate({
            'email_type': 'string|in:welcome,otp,security_alert,account_change',
            'message': 'string'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            email_type = request.input('email_type', 'welcome')
            message = request.input('message', 'This is a test email')

            # Queue test email
            success = False
            if email_type == 'welcome':
                success = self.queue_service.queue_welcome_email(user.id)
            elif email_type == 'otp':
                success = self.queue_service.queue_otp_email(user.id, '123456', 'test')
            elif email_type == 'security_alert':
                success = self.queue_service.queue_security_alert_email(user.id, 'Test Alert', message)
            elif email_type == 'account_change':
                success = self.queue_service.queue_account_change_email(user.id, 'test_change', message)

            if success:
                return response.json({
                    'message': f'Test {email_type} email queued successfully'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 500,
                        'name': 'InternalServerError',
                        'message': 'Failed to queue test email'
                    }
                }, 500)

        except Exception as e:
            print(f"❌ Queue Test Email Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to queue test email'
                }
            }, 500)

    def trigger_data_cleanup(self, request: Request, response: Response, validate: Validator, auth: Auth):
        """
        POST /api/queue/cleanup
        Trigger data cleanup job (admin only)
        """
        # Validate request
        errors = validate.validate({
            'cleanup_type': 'string|in:all,security_events,otps,users,tokens',
            'days_to_keep': 'integer|min:1|max:365'
        })

        if errors:
            return response.json({
                'error': {
                    'statusCode': 400,
                    'name': 'ValidationError',
                    'message': 'Invalid request data',
                    'details': errors
                }
            }, 400)

        try:
            # Get current user (admin check would go here)
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            cleanup_type = request.input('cleanup_type', 'all')
            days_to_keep = request.input('days_to_keep', 90)

            # Queue cleanup job
            success = self.queue_service.queue_data_cleanup(cleanup_type, days_to_keep)

            if success:
                return response.json({
                    'message': f'Data cleanup job queued: {cleanup_type}'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 500,
                        'name': 'InternalServerError',
                        'message': 'Failed to queue cleanup job'
                    }
                }, 500)

        except Exception as e:
            print(f"❌ Queue Cleanup Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to queue cleanup job'
                }
            }, 500)

    def process_security_events(self, request: Request, response: Response, auth: Auth):
        """
        POST /api/queue/process-security-events
        Process high-priority security events (admin only)
        """
        try:
            # Get current user (admin check would go here)
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Process high-priority security events
            count = self.queue_service.process_high_priority_security_events()

            return response.json({
                'message': f'Queued {count} high-priority security events for processing'
            }, 200)

        except Exception as e:
            print(f"❌ Process Security Events Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to process security events'
                }
            }, 500)

    def get_queue_status(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/queue/status
        Get queue system status
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get queue status
            status = self.queue_service.get_queue_status()

            return response.json({
                'status': 'operational',
                'queue_driver': 'async',
                'workers_active': True,
                'last_check': str(datetime.now()),
                'details': status
            }, 200)

        except Exception as e:
            print(f"❌ Queue Status Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get queue status'
                }
            }, 500)

    def get_failed_jobs(self, request: Request, response: Response, auth: Auth):
        """
        GET /api/queue/failed-jobs
        Get failed jobs list
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Get failed jobs (simulated for now)
            failed_jobs = []  # In real implementation, this would query the failed_jobs table

            return response.json({
                'failed_jobs': failed_jobs,
                'total': len(failed_jobs)
            }, 200)

        except Exception as e:
            print(f"❌ Failed Jobs Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to get failed jobs'
                }
            }, 500)

    def test_security_processing(self, request: Request, response: Response, auth: Auth):
        """
        POST /api/queue/test-security-processing
        Test security event processing
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Queue test security processing
            success = self.queue_service.queue_security_event_processing(
                event_id=1,  # Test event ID
                action_type="analyze"
            )

            if success:
                return response.json({
                    'message': 'Test security processing job queued successfully'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 500,
                        'name': 'InternalServerError',
                        'message': 'Failed to queue security processing job'
                    }
                }, 500)

        except Exception as e:
            print(f"❌ Test Security Processing Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to test security processing'
                }
            }, 500)

    def test_cleanup(self, request: Request, response: Response, auth: Auth):
        """
        POST /api/queue/test-cleanup
        Test data cleanup job
        """
        try:
            # Get current user
            user = request.user()
            if not user:
                return response.json({
                    'error': {
                        'statusCode': 401,
                        'name': 'UnauthorizedError',
                        'message': 'Authentication required'
                    }
                }, 401)

            # Queue test cleanup job
            success = self.queue_service.queue_data_cleanup(
                cleanup_type="security_events",
                days_to_keep=30
            )

            if success:
                return response.json({
                    'message': 'Test cleanup job queued successfully'
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 500,
                        'name': 'InternalServerError',
                        'message': 'Failed to queue test cleanup job'
                    }
                }, 500)

        except Exception as e:
            print(f"❌ Test Cleanup Error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Failed to test cleanup'
                }
            }, 500)
