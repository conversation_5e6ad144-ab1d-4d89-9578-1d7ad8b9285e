"""Security Service for advanced security features"""

from datetime import datetime, timedelta, timezone
from app.models.User import User
from app.models.SecurityEvent import SecurityEvent, SecurityEventType
from app.services.NotificationService import NotificationService
from masonite.cache import Cache
import re
import json


class SecurityService:
    """Service for handling advanced security features"""

    def __init__(self):
        self.cache = Cache()
        self.notification_service = NotificationService()

    def check_account_lockout(self, user_id, ip_address=None):
        """Check if account should be locked due to failed attempts"""
        try:
            user = User.find(user_id)
            if not user:
                return False

            # Check if already locked
            if hasattr(user, 'locked_until') and user.locked_until:
                if isinstance(user.locked_until, str):
                    locked_until = datetime.fromisoformat(user.locked_until.replace('Z', '+00:00'))
                else:
                    locked_until = user.locked_until
                
                if datetime.now(timezone.utc) < locked_until:
                    return True
                else:
                    # Lock period expired, reset
                    user.locked_until = None
                    user.login_attempts = 0
                    user.save()

            # Check login attempts
            max_attempts = 5  # Configurable
            lockout_duration = 30  # minutes, configurable
            
            if hasattr(user, 'login_attempts') and user.login_attempts >= max_attempts:
                # Lock the account
                user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=lockout_duration)
                user.save()

                # Log the lockout event
                SecurityEvent.log_account_locked(
                    user_id=user.id,
                    reason=f"Too many failed login attempts ({user.login_attempts})",
                    ip_address=ip_address
                )

                # Send account locked notification
                self.notification_service.send_account_locked_alert(
                    user_id=user.id,
                    reason=f"Too many failed login attempts ({user.login_attempts})",
                    locked_until=user.locked_until
                )

                return True

            return False

        except Exception as e:
            print(f"❌ Account lockout check error: {str(e)}")
            return False

    def increment_login_attempts(self, user_id, ip_address=None, user_agent=None):
        """Increment failed login attempts for user"""
        try:
            user = User.find(user_id)
            if not user:
                return

            # Increment attempts
            if not hasattr(user, 'login_attempts'):
                user.login_attempts = 0
            user.login_attempts += 1
            user.save()

            # Log failed attempt
            SecurityEvent.log_login_failed(
                identifier=user.email,
                reason="Invalid credentials",
                ip_address=ip_address,
                user_agent=user_agent,
                user_id=user.id
            )

            # Check if account should be locked
            self.check_account_lockout(user.id, ip_address)

        except Exception as e:
            print(f"❌ Login attempt increment error: {str(e)}")

    def reset_login_attempts(self, user_id, ip_address=None, user_agent=None):
        """Reset login attempts on successful login"""
        try:
            user = User.find(user_id)
            if not user:
                return

            # Reset attempts and unlock
            if hasattr(user, 'login_attempts'):
                user.login_attempts = 0
            if hasattr(user, 'locked_until'):
                user.locked_until = None
            user.last_login_at = datetime.now(timezone.utc)
            user.save()

            # Log successful login
            SecurityEvent.log_login_success(
                user_id=user.id,
                ip_address=ip_address,
                user_agent=user_agent
            )

            # Send login alert notification
            self.notification_service.send_login_alert(
                user_id=user.id,
                ip_address=ip_address,
                user_agent=user_agent
            )

        except Exception as e:
            print(f"❌ Login attempt reset error: {str(e)}")

    def detect_suspicious_activity(self, user_id, ip_address, user_agent=None, endpoint=None):
        """Detect suspicious activity patterns"""
        try:
            suspicious_indicators = []

            # Check for rapid requests from same IP
            cache_key = f"requests:{ip_address}"
            request_count = self.cache.get(cache_key, 0)
            
            if request_count > 100:  # More than 100 requests in the time window
                suspicious_indicators.append("High request frequency")

            # Check for multiple failed logins from same IP
            recent_failures = SecurityEvent.where('event_type', SecurityEventType.LOGIN_FAILED.value)\
                                          .where('ip_address', ip_address)\
                                          .where('created_at', '>', datetime.now(timezone.utc) - timedelta(hours=1))\
                                          .count()
            
            if recent_failures > 10:
                suspicious_indicators.append("Multiple failed login attempts")

            # Check for unusual user agent patterns
            if user_agent:
                if self._is_suspicious_user_agent(user_agent):
                    suspicious_indicators.append("Suspicious user agent")

            # Check for access from multiple IPs in short time
            if user_id:
                recent_ips = SecurityEvent.where('user_id', str(user_id))\
                                         .where('created_at', '>', datetime.now(timezone.utc) - timedelta(minutes=30))\
                                         .distinct('ip_address')\
                                         .count()
                
                if recent_ips > 3:
                    suspicious_indicators.append("Multiple IP addresses")

            # Log suspicious activity if detected
            if suspicious_indicators:
                SecurityEvent.log_suspicious_activity(
                    description=", ".join(suspicious_indicators),
                    user_id=user_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    event_data={'indicators': suspicious_indicators, 'endpoint': endpoint}
                )

                # Send suspicious activity alert
                if user_id:
                    self.notification_service.send_suspicious_activity_alert(
                        user_id=user_id,
                        activity_description=", ".join(suspicious_indicators),
                        details={'indicators': suspicious_indicators, 'endpoint': endpoint}
                    )

                return True

            return False

        except Exception as e:
            print(f"❌ Suspicious activity detection error: {str(e)}")
            return False

    def _is_suspicious_user_agent(self, user_agent):
        """Check if user agent appears suspicious"""
        suspicious_patterns = [
            r'bot',
            r'crawler',
            r'spider',
            r'scraper',
            r'curl',
            r'wget',
            r'python',
            r'requests',
            r'http',
            r'^$',  # Empty user agent
        ]
        
        user_agent_lower = user_agent.lower()
        for pattern in suspicious_patterns:
            if re.search(pattern, user_agent_lower):
                return True
        
        return False

    def track_rate_limit_violation(self, endpoint, ip_address, user_agent=None, user_id=None):
        """Track rate limit violations"""
        try:
            SecurityEvent.log_rate_limit_exceeded(
                endpoint=endpoint,
                ip_address=ip_address,
                user_agent=user_agent,
                user_id=user_id
            )

            # Check if this IP has too many rate limit violations
            recent_violations = SecurityEvent.where('event_type', SecurityEventType.RATE_LIMIT_EXCEEDED.value)\
                                           .where('ip_address', ip_address)\
                                           .where('created_at', '>', datetime.now(timezone.utc) - timedelta(hours=1))\
                                           .count()
            
            if recent_violations > 20:  # Too many violations
                SecurityEvent.log_suspicious_activity(
                    description=f"Excessive rate limit violations ({recent_violations})",
                    ip_address=ip_address,
                    user_agent=user_agent,
                    user_id=user_id,
                    event_data={'violations_count': recent_violations}
                )

        except Exception as e:
            print(f"❌ Rate limit tracking error: {str(e)}")

    def get_security_dashboard_data(self, user_id=None):
        """Get security dashboard data"""
        try:
            # Get recent security events
            recent_events = SecurityEvent.order_by('created_at', 'desc').limit(50).get()
            
            # Get unresolved high-priority events
            unresolved_events = SecurityEvent.get_unresolved_events()
            
            # Get user-specific events if user_id provided
            user_events = []
            if user_id:
                user_events = SecurityEvent.get_events_by_user(user_id, 20)

            # Get statistics
            stats = {
                'total_events_today': SecurityEvent.where('created_at', '>', datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)).count(),
                'failed_logins_today': SecurityEvent.where('event_type', SecurityEventType.LOGIN_FAILED.value)\
                                                  .where('created_at', '>', datetime.now(timezone.utc).replace(hour=0, minute=0, second=0))\
                                                  .count(),
                'locked_accounts': User.where('locked_until', '>', datetime.now(timezone.utc)).count() if hasattr(User, 'locked_until') else 0,
                'unresolved_events': len(unresolved_events),
                'high_risk_events': SecurityEvent.where('risk_level', 'high').or_where('risk_level', 'critical').count()
            }

            return {
                'recent_events': [self._format_event(event) for event in recent_events],
                'unresolved_events': [self._format_event(event) for event in unresolved_events],
                'user_events': [self._format_event(event) for event in user_events],
                'statistics': stats
            }

        except Exception as e:
            print(f"❌ Security dashboard error: {str(e)}")
            return {
                'recent_events': [],
                'unresolved_events': [],
                'user_events': [],
                'statistics': {}
            }

    def _format_event(self, event):
        """Format security event for API response"""
        return {
            'id': event.id,
            'event_type': event.event_type,
            'severity': event.severity,
            'message': event.message,
            'user_id': event.user_id,
            'ip_address': event.ip_address,
            'risk_level': event.risk_level,
            'requires_action': event.requires_action,
            'resolved': event.resolved,
            'created_at': str(event.created_at),
            'event_data': event.get_event_data_dict()
        }



    def is_account_locked(self, user_id):
        """Check if account is currently locked"""
        try:
            user = User.find(user_id)
            if not user or not hasattr(user, 'locked_until') or not user.locked_until:
                return False

            if isinstance(user.locked_until, str):
                locked_until = datetime.fromisoformat(user.locked_until.replace('Z', '+00:00'))
            else:
                locked_until = user.locked_until

            return datetime.now(timezone.utc) < locked_until

        except Exception as e:
            print(f"❌ Account lock check error: {str(e)}")
            return False

    def unlock_account(self, user_id, unlocked_by=None):
        """Manually unlock an account"""
        try:
            user = User.find(user_id)
            if not user:
                return False

            # Reset lock status
            if hasattr(user, 'locked_until'):
                user.locked_until = None
            if hasattr(user, 'login_attempts'):
                user.login_attempts = 0
            user.save()

            # Log the unlock event
            SecurityEvent.log_event(
                SecurityEventType.ACCOUNT_UNLOCKED.value,
                f"Account {user_id} manually unlocked",
                user_id=user.id,
                event_data={'unlocked_by': unlocked_by},
                severity="info"
            )

            return True

        except Exception as e:
            print(f"❌ Account unlock error: {str(e)}")
            return False

    def get_security_analysis(self, user_id=None):
        """Get security analysis data"""
        try:
            analysis = {
                'risk_level': 'low',
                'threat_indicators': [],
                'security_score': 85,
                'recommendations': [],
                'recent_threats': 0,
                'blocked_attempts': 0,
                'analysis_timestamp': str(datetime.now(timezone.utc))
            }

            if user_id:
                # Get user-specific analysis
                user_events = SecurityEvent.where('user_id', str(user_id))\
                                         .where('created_at', '>', datetime.now(timezone.utc) - timedelta(days=7))\
                                         .get()

                failed_logins = len([e for e in user_events if e.event_type == 'login_failed'])
                suspicious_activities = len([e for e in user_events if e.event_type == 'suspicious_activity'])

                if failed_logins > 5:
                    analysis['risk_level'] = 'medium'
                    analysis['threat_indicators'].append('Multiple failed login attempts')

                if suspicious_activities > 0:
                    analysis['risk_level'] = 'high'
                    analysis['threat_indicators'].append('Suspicious activity detected')

                analysis['recent_threats'] = failed_logins + suspicious_activities
                analysis['security_score'] = max(0, 100 - (failed_logins * 5) - (suspicious_activities * 10))

            return analysis

        except Exception as e:
            print(f"❌ Security analysis error: {str(e)}")
            return {
                'risk_level': 'unknown',
                'threat_indicators': [],
                'security_score': 0,
                'recommendations': [],
                'recent_threats': 0,
                'blocked_attempts': 0,
                'analysis_timestamp': str(datetime.now(timezone.utc)),
                'error': str(e)
            }

    def cleanup_security_data(self):
        """Clean up old security data"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=90)

            # Clean up resolved security events older than 90 days
            deleted_events = SecurityEvent.where('resolved', True)\
                                        .where('created_at', '<', cutoff_date)\
                                        .delete()

            return {
                'message': 'Security data cleanup completed',
                'deleted_events': deleted_events,
                'cleanup_date': str(cutoff_date)
            }

        except Exception as e:
            print(f"❌ Security cleanup error: {str(e)}")
            return {
                'message': 'Security data cleanup failed',
                'error': str(e)
            }
