"""Queue Service for managing background jobs and queue operations"""

from masonite.queues import Queue
from app.jobs.SendEmailJob import Send<PERSON><PERSON><PERSON>ob
from app.jobs.ProcessSecurityEventJob import ProcessSecurityEventJob
from app.jobs.DataCleanupJob import DataCleanupJob
from app.models.SecurityEvent import SecurityEvent
from datetime import datetime, timezone


class QueueService:
    """Service for managing queue operations and background jobs"""

    def __init__(self):
        self.queue = Queue()

    def queue_email(self, email_type, recipient_id, data=None, template=None, delay=None):
        """Queue an email to be sent in the background"""
        try:
            job = SendEmailJob(email_type, recipient_id, data, template)
            
            if delay:
                # Schedule job for later
                self.queue.push(job, delay=delay)
                print(f"📧 Email job scheduled with delay: {email_type} for user {recipient_id}")
            else:
                # Queue job immediately
                self.queue.push(job)
                print(f"📧 Email job queued: {email_type} for user {recipient_id}")
            
            return True

        except Exception as e:
            print(f"❌ Failed to queue email job: {str(e)}")
            return False

    def queue_security_event_processing(self, event_id, action_type="analyze", delay=None):
        """Queue security event processing"""
        try:
            job = ProcessSecurityEventJob(event_id, action_type)
            
            if delay:
                self.queue.push(job, delay=delay)
                print(f"🔍 Security event processing scheduled: {event_id} ({action_type})")
            else:
                self.queue.push(job)
                print(f"🔍 Security event processing queued: {event_id} ({action_type})")
            
            return True

        except Exception as e:
            print(f"❌ Failed to queue security event processing: {str(e)}")
            return False

    def queue_data_cleanup(self, cleanup_type="all", days_to_keep=90, delay=None):
        """Queue data cleanup job"""
        try:
            job = DataCleanupJob(cleanup_type, days_to_keep)
            
            if delay:
                self.queue.push(job, delay=delay)
                print(f"🧹 Data cleanup scheduled: {cleanup_type}")
            else:
                self.queue.push(job)
                print(f"🧹 Data cleanup queued: {cleanup_type}")
            
            return True

        except Exception as e:
            print(f"❌ Failed to queue data cleanup: {str(e)}")
            return False

    def queue_otp_email(self, user_id, otp_code, otp_type="login", expires_in_minutes=10):
        """Queue OTP email sending"""
        try:
            data = {
                'otp_code': otp_code,
                'otp_type': otp_type,
                'expires_in_minutes': expires_in_minutes
            }
            
            return self.queue_email("otp", user_id, data)

        except Exception as e:
            print(f"❌ Failed to queue OTP email: {str(e)}")
            return False

    def queue_security_alert_email(self, user_id, alert_type, message, severity="medium"):
        """Queue security alert email"""
        try:
            data = {
                'alert_type': alert_type,
                'message': message,
                'severity': severity
            }
            
            return self.queue_email("security_alert", user_id, data)

        except Exception as e:
            print(f"❌ Failed to queue security alert email: {str(e)}")
            return False

    def queue_account_change_email(self, user_id, change_type, description):
        """Queue account change email"""
        try:
            data = {
                'change_type': change_type,
                'description': description
            }
            
            return self.queue_email("account_change", user_id, data)

        except Exception as e:
            print(f"❌ Failed to queue account change email: {str(e)}")
            return False

    def queue_welcome_email(self, user_id):
        """Queue welcome email for new users"""
        try:
            return self.queue_email("welcome", user_id)

        except Exception as e:
            print(f"❌ Failed to queue welcome email: {str(e)}")
            return False

    def queue_password_reset_email(self, user_id, reset_token):
        """Queue password reset email"""
        try:
            data = {
                'reset_token': reset_token
            }
            
            return self.queue_email("password_reset", user_id, data)

        except Exception as e:
            print(f"❌ Failed to queue password reset email: {str(e)}")
            return False

    def schedule_daily_cleanup(self):
        """Schedule daily data cleanup"""
        try:
            # Schedule cleanup for different data types at different times
            # to spread the load
            
            # Security events cleanup at 2 AM
            self.queue_data_cleanup("security_events", days_to_keep=90, delay=7200)  # 2 hours
            
            # OTP cleanup at 3 AM
            self.queue_data_cleanup("otps", delay=10800)  # 3 hours
            
            # Token cleanup at 4 AM
            self.queue_data_cleanup("tokens", delay=14400)  # 4 hours
            
            print("📅 Daily cleanup jobs scheduled")
            return True

        except Exception as e:
            print(f"❌ Failed to schedule daily cleanup: {str(e)}")
            return False

    def process_high_priority_security_events(self):
        """Process high-priority security events immediately"""
        try:
            # Get unresolved high-risk events
            high_risk_events = SecurityEvent.where('risk_level', 'high')\
                                           .or_where('risk_level', 'critical')\
                                           .where('resolved', False)\
                                           .get()
            
            count = 0
            for event in high_risk_events:
                # Queue immediate processing
                if self.queue_security_event_processing(event.id, "analyze"):
                    count += 1
            
            print(f"⚡ Queued {count} high-priority security events for processing")
            return count

        except Exception as e:
            print(f"❌ Failed to process high-priority events: {str(e)}")
            return 0

    def queue_bulk_emails(self, email_type, user_ids, data=None):
        """Queue bulk emails for multiple users"""
        try:
            success_count = 0
            
            for user_id in user_ids:
                if self.queue_email(email_type, user_id, data):
                    success_count += 1
            
            print(f"📧 Queued {success_count}/{len(user_ids)} bulk emails")
            return success_count

        except Exception as e:
            print(f"❌ Failed to queue bulk emails: {str(e)}")
            return 0

    def get_queue_stats(self):
        """Get queue statistics (if supported by driver)"""
        try:
            # This would return queue statistics if the driver supports it
            # For now, return basic info
            
            stats = {
                'queue_driver': 'async',  # Default driver
                'jobs_queued': 'N/A',     # Would need driver support
                'jobs_failed': 'N/A',     # Would need driver support
                'workers_active': 'N/A'   # Would need driver support
            }
            
            return stats

        except Exception as e:
            print(f"❌ Failed to get queue stats: {str(e)}")
            return {}

    def retry_failed_jobs(self):
        """Retry failed jobs (if using database driver)"""
        try:
            # This would retry failed jobs if using database driver
            # For now, just log the attempt
            
            print("🔄 Failed job retry not implemented for current driver")
            return 0

        except Exception as e:
            print(f"❌ Failed to retry jobs: {str(e)}")
            return 0

    def clear_queue(self, queue_name="default"):
        """Clear all jobs from a queue (use with caution)"""
        try:
            # This would clear the queue if the driver supports it
            # For now, just log the attempt
            
            print(f"🗑️ Queue clearing not implemented for current driver: {queue_name}")
            return False

        except Exception as e:
            print(f"❌ Failed to clear queue: {str(e)}")
            return False

    def queue_custom_job(self, job_class, *args, **kwargs):
        """Queue a custom job class"""
        try:
            job = job_class(*args, **kwargs)
            self.queue.push(job)
            
            print(f"⚙️ Custom job queued: {job_class.__name__}")
            return True

        except Exception as e:
            print(f"❌ Failed to queue custom job: {str(e)}")
            return False

    def schedule_recurring_tasks(self):
        """Schedule recurring background tasks"""
        try:
            # This would set up recurring tasks
            # For now, just schedule some basic tasks
            
            # Daily cleanup
            self.schedule_daily_cleanup()
            
            # Process high-priority events every hour
            # Note: This would need a proper scheduler like Celery Beat
            # For now, just queue immediate processing
            self.process_high_priority_security_events()
            
            print("📅 Recurring tasks scheduled")
            return True

        except Exception as e:
            print(f"❌ Failed to schedule recurring tasks: {str(e)}")
            return False
